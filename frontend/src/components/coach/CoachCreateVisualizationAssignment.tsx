import { useState, useEffect } from "react";
import { useNavigate, useSearchParams, Link } from "react-router-dom";
import { PageHeader } from "../ui/PageHeader";
import { Card, CardHeader } from "../ui/Card";
import { Button } from "../ui/Button";
import SelectInput from "../ui/input/SelectInput";

import { useCoach } from "../../context/CoachContext";
import { LoadingSpinner } from "../ui/LoadingSpinner";
import { ErrorMessage } from "../ui/ErrorMessage";
import { CheckCircle, Calendar, User, Eye, Play } from "lucide-react";
import DateInput from "../ui/input/DateInput";

const CoachCreateVisualizationAssignment = () => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const preselectedVisualizationId = searchParams.get("visualizationId");

  const {
    myCoachees,
    allVisualizations,
    loading,
    fetchMyCoachees,
    fetchAllVisualizations,
    createNewVisualizationAssignment,
  } = useCoach();

  const [formData, setFormData] = useState({
    visualizationId: preselectedVisualizationId || "",
    coacheeId: "",
    dueDate: "",
  });

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  useEffect(() => {
    fetchMyCoachees();
    fetchAllVisualizations();
  }, []);

  const handleInputChange = (field: string, value: string) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
    setError(null);
    setSuccess(null);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!formData.visualizationId || !formData.coacheeId) {
      setError("Please select both a visualization and a coachee");
      return;
    }

    setIsSubmitting(true);
    setError(null);
    setSuccess(null);

    try {
      await createNewVisualizationAssignment({
        visualizationId: formData.visualizationId,
        coacheeId: formData.coacheeId,
        dueDate: formData.dueDate || undefined,
      });

      const selectedVisualization = allVisualizations.find(
        (v) => v.id === formData.visualizationId
      );
      const selectedCoachee = myCoachees.find(
        (c) => c.id === formData.coacheeId
      );

      setSuccess(
        `Successfully assigned "${selectedVisualization?.title}" to ${selectedCoachee?.firstName} ${selectedCoachee?.lastName}`
      );

      // Reset form
      setFormData({
        visualizationId: "",
        coacheeId: "",
        dueDate: "",
      });

      // Redirect after a short delay
      setTimeout(() => {
        navigate("/dashboard/coachees");
      }, 2000);
    } catch (error: any) {
      setError(
        error.response?.data?.error ||
          "Failed to create visualization assignment"
      );
    } finally {
      setIsSubmitting(false);
    }
  };

  const selectedVisualization = allVisualizations.find(
    (v) => v.id === formData.visualizationId
  );
  const selectedCoachee = myCoachees.find((c) => c.id === formData.coacheeId);

  if (loading.visualizations || loading.coachees) {
    return <LoadingSpinner />;
  }

  return (
    <div className="space-y-6">
      <PageHeader
        title="Create Imagery Exercise Assignment"
        description="Assign an imagery exercise to one of your coachees."
      />

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Assignment Form */}
        <Card
          header={
            <CardHeader
              title="Assignment Details"
              description="Select the imagery exercise and coachee for this assignment"
            />
          }
        >
          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Visualization Selection */}
            <div>
              <SelectInput
                label="Visualization"
                value={formData.visualizationId}
                onChange={(value) =>
                  handleInputChange("visualizationId", value)
                }
                options={[
                  { value: "", label: "Select an imagery exercise..." },
                  ...allVisualizations.map((visualization) => ({
                    value: visualization.id,
                    label: visualization.title,
                  })),
                ]}
              />
              {allVisualizations.length === 0 && (
                <div className="mt-2 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                  <p className="text-sm text-yellow-800">
                    No visualizations available yet.{" "}
                    <Link
                      to="/dashboard/create-visualization"
                      className="font-medium underline hover:no-underline"
                    >
                      Create one now
                    </Link>
                  </p>
                </div>
              )}
              <div className="mt-2">
                <Link
                  to="/dashboard/create-visualization"
                  className="text-sm text-blue-600 hover:text-blue-800 underline"
                >
                  + Create a new imagery exercise
                </Link>
              </div>
              {selectedVisualization && (
                <div className="mt-2 p-3 bg-purple-50 border border-purple-200 rounded-lg">
                  <div className="flex items-center">
                    <Eye className="h-4 w-4 text-purple-600 mr-2" />
                    <span className="text-sm font-medium text-purple-900">
                      {selectedVisualization.title}
                    </span>
                  </div>
                  <p className="mt-1 text-sm text-purple-700">
                    {selectedVisualization.description}
                  </p>
                  {selectedVisualization.audioUrl && (
                    <div className="mt-2 flex items-center text-sm text-purple-600">
                      <Play className="h-3 w-3 mr-1" />
                      <span>Audio available</span>
                    </div>
                  )}
                </div>
              )}
            </div>

            {/* Coachee Selection */}
            <div>
              <SelectInput
                label="Coachee"
                value={formData.coacheeId}
                onChange={(value) => handleInputChange("coacheeId", value)}
                options={[
                  { value: "", label: "Select a coachee..." },
                  ...myCoachees.map((coachee) => ({
                    value: coachee.id,
                    label: `${coachee.firstName} ${coachee.lastName} (${coachee.email})`,
                  })),
                ]}
                required
              />
              {selectedCoachee && (
                <div className="mt-2 p-3 bg-green-50 border border-green-200 rounded-lg">
                  <div className="flex items-center">
                    <User className="h-4 w-4 text-green-600 mr-2" />
                    <span className="text-sm font-medium text-green-900">
                      {selectedCoachee.firstName} {selectedCoachee.lastName}
                    </span>
                  </div>
                  <p className="mt-1 text-sm text-green-700">
                    {selectedCoachee.email}
                  </p>
                </div>
              )}
            </div>

            {/* Due Date */}
            <div>
              <div className="flex flex-col w-full my-2">
                <DateInput
                  label="Due Date (Optional)"
                  value={formData.dueDate}
                  onChange={(value) => handleInputChange("dueDate", value)}
                  min={new Date().toISOString().split("T")[0]}
                />
              </div>
              {formData.dueDate && (
                <div className="mt-2 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                  <div className="flex items-center">
                    <Calendar className="h-4 w-4 text-yellow-600 mr-2" />
                    <span className="text-sm font-medium text-yellow-900">
                      Due: {new Date(formData.dueDate).toLocaleDateString()}
                    </span>
                  </div>
                </div>
              )}
            </div>

            {error && <ErrorMessage message={error} />}

            {success && (
              <div className="flex items-center p-3 bg-green-50 border border-green-200 rounded-lg">
                <CheckCircle className="h-5 w-5 text-green-600 mr-2" />
                <span className="text-sm text-green-800">{success}</span>
              </div>
            )}

            <div className="flex space-x-3">
              <Button
                type="submit"
                disabled={
                  isSubmitting ||
                  !formData.visualizationId ||
                  !formData.coacheeId
                }
                className="flex-1"
              >
                {isSubmitting
                  ? "Creating Assignment..."
                  : "Create Imagery Exercise Assignment"}
              </Button>
              <Button
                type="button"
                variant="secondary"
                onClick={() => navigate("/dashboard")}
              >
                Cancel
              </Button>
            </div>
          </form>
        </Card>

        {/* Assignment Preview */}
        <Card
          header={
            <CardHeader
              title="Assignment Preview"
              description="Review the assignment details before creating"
            />
          }
        >
          <div className="space-y-4">
            {!formData.visualizationId && !formData.coacheeId ? (
              <div className="text-center py-8">
                <Eye className="mx-auto h-12 w-12 text-gray-400" />
                <h3 className="mt-2 text-sm font-medium text-gray-900">
                  No assignment details yet
                </h3>
                <p className="mt-1 text-sm text-gray-500">
                  Select a visualization and coachee to see the preview.
                </p>
              </div>
            ) : (
              <div className="space-y-4">
                <div>
                  <h4 className="text-sm font-medium text-gray-900 mb-2">
                    Assignment Summary
                  </h4>
                  <div className="bg-gray-50 p-3 rounded-lg space-y-2">
                    <div>
                      <span className="text-xs font-medium text-gray-500">
                        VISUALIZATION
                      </span>
                      <p className="text-sm text-gray-900">
                        {selectedVisualization?.title || "Not selected"}
                      </p>
                    </div>
                    <div>
                      <span className="text-xs font-medium text-gray-500">
                        COACHEE
                      </span>
                      <p className="text-sm text-gray-900">
                        {selectedCoachee
                          ? `${selectedCoachee.firstName} ${selectedCoachee.lastName}`
                          : "Not selected"}
                      </p>
                    </div>
                    <div>
                      <span className="text-xs font-medium text-gray-500">
                        DUE DATE
                      </span>
                      <p className="text-sm text-gray-900">
                        {formData.dueDate
                          ? new Date(formData.dueDate).toLocaleDateString()
                          : "No due date"}
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
        </Card>
      </div>
    </div>
  );
};

export default CoachCreateVisualizationAssignment;
