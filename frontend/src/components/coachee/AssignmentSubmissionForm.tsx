import React, { useState, useEffect } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { useCoachee } from "../../context/CoacheeContext";
import { LoadingSpinner } from "../ui/LoadingSpinner";
import { Card, CardHeader } from "../ui/Card";
import { Button } from "../ui/Button";
import { ErrorMessage } from "../ui/ErrorMessage";
import { PageHeader } from "../ui/PageHeader";
import RichTextDisplay from "../ui/RichTextDisplay";
import { TextResponse } from "../ui/form/TextResponse";
import { LikertResponse } from "../ui/form/LikertResponse";
import { TaskResponse } from "../ui/form/TaskResponse";
import { TableResponse } from "../ui/form/TableResponse";
import { Graph2DResponse } from "../ui/form/Graph2DResponse";
import { Question } from "../../types/exercise.types";
import { SubmitAnswersData } from "../../types/api/assignments.types";
import { CheckCircle, Clock, Calendar, User } from "lucide-react";

const AssignmentSubmissionForm = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const {
    currentAssignment,
    loading,
    fetchAssignmentById,
    submitAssignment,
    clearCurrentAssignment,
  } = useCoachee();

  const [answers, setAnswers] = useState<Record<string, any>>({});
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  useEffect(() => {
    if (id) {
      fetchAssignmentById(id);
    }
    return () => {
      clearCurrentAssignment();
    };
  }, [id]);

  if (loading.assignment) {
    return <LoadingSpinner />;
  }

  if (!currentAssignment) {
    return (
      <div className="text-center py-12">
        <h3 className="text-lg font-medium text-gray-900 mb-2">
          Assignment not found
        </h3>
        <p className="text-gray-500 mb-4">
          The assignment you're looking for doesn't exist or you don't have
          access to it.
        </p>
        <Button onClick={() => navigate("/dashboard/assignments")}>
          Back to Assignments
        </Button>
      </div>
    );
  }

  const isCompleted = currentAssignment.status === "COMPLETED";
  const questions: Question[] = (
    currentAssignment.exercise.questions || []
  ).map((question, index) => ({
    ...question,
    // Ensure each question has a unique ID for React keys and state management
    id: question.id || `question-${index}`,
  }));

  const handleAnswerChange = (questionId: string, value: any) => {
    setAnswers((prev) => ({
      ...prev,
      [questionId]: value,
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);
    setSuccess(null);

    if (!id) return;

    try {
      // Convert answers to the expected format
      const submissionData: SubmitAnswersData = {
        answers: questions.map((question) => {
          const answer = answers[question.id];
          const baseAnswer = { questionId: question.id };

          switch (question.type) {
            case "free_text":
              return { ...baseAnswer, responseText: answer || "" };
            case "likert":
              return { ...baseAnswer, likertResponse: answer || 1 };
            case "task":
              return { ...baseAnswer, taskCompleted: answer || false };
            case "table":
              return { ...baseAnswer, tableResponse: answer || [] };
            case "graph_2d":
              return { ...baseAnswer, graphResponse: answer || [] };
            default:
              return { ...baseAnswer, responseText: answer || "" };
          }
        }),
      };

      await submitAssignment(id, submissionData);
      setSuccess("Assignment submitted successfully!");

      // Redirect after a short delay
      setTimeout(() => {
        navigate("/dashboard/assignments");
      }, 2000);
    } catch (error: any) {
      setError(error.response?.data?.error || "Failed to submit assignment");
    }
  };

  const renderQuestion = (question: Question) => {
    const value = answers[question.id];

    console.log(question.id, value);

    switch (question.type) {
      case "text_block":
        // This shouldn't be called for text blocks, but just in case
        return null;
      case "free_text":
        return (
          <TextResponse
            value={value || ""}
            onChange={(newValue) => handleAnswerChange(question.id, newValue)}
            isCompleted={isCompleted}
            saving={loading.submitting}
          />
        );

      case "likert":
        return (
          <LikertResponse
            scaleMin={question.scaleMin || 1}
            scaleMax={question.scaleMax || 5}
            labels={question.labels || {}}
            responseMode={(question as any).responseMode || "single"}
            multiResponseLabels={(question as any).multiResponseLabels}
            value={value || question.scaleMin || 1}
            onChange={(newValue) => handleAnswerChange(question.id, newValue)}
            isCompleted={isCompleted}
            saving={loading.submitting}
          />
        );

      case "task":
        return (
          <TaskResponse
            completed={value || false}
            onChange={(newValue) => handleAnswerChange(question.id, newValue)}
            isCompleted={isCompleted}
            saving={loading.submitting}
          />
        );

      case "table":
        return (
          <TableResponse
            columns={question.columns || []}
            value={value || []}
            onChange={(newValue) => handleAnswerChange(question.id, newValue)}
            isCompleted={isCompleted}
            saving={loading.submitting}
            rowMode={question.rowMode || "dynamic"}
            fixedRows={
              question.rowMode === "fixed" ? question.fixedRows : undefined
            }
          />
        );

      case "graph_2d":
        return (
          <Graph2DResponse
            xAxisLabel={question.xAxisLabel || "X Axis"}
            yAxisLabel={question.yAxisLabel || "Y Axis"}
            xAxisMin={question.xAxisMin || 0}
            xAxisMax={question.xAxisMax || 10}
            yAxisMin={question.yAxisMin || 0}
            yAxisMax={question.yAxisMax || 10}
            value={value || []}
            onChange={(newValue) => handleAnswerChange(question.id, newValue)}
            isCompleted={isCompleted}
            saving={loading.submitting}
          />
        );

      default:
        return (
          <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-md">
            <p className="text-sm text-yellow-800">
              Unsupported question type: {(question as any).type}
            </p>
          </div>
        );
    }
  };

  return (
    <div className="space-y-6">
      <PageHeader title={currentAssignment.exercise.name} />

      {/* Assignment Info */}
      <Card>
        <CardHeader title="Assignment Details" />
        <div className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="flex items-center">
              <User className="h-4 w-4 text-gray-400 mr-2" />
              <div>
                <p className="text-sm font-medium text-gray-900">Status</p>
                <div className="flex items-center">
                  {isCompleted ? (
                    <CheckCircle className="h-4 w-4 text-green-600 mr-1" />
                  ) : (
                    <Clock className="h-4 w-4 text-yellow-600 mr-1" />
                  )}
                  <span
                    className={`text-sm ${
                      isCompleted ? "text-green-600" : "text-yellow-600"
                    }`}
                  >
                    {currentAssignment.status.replace("_", " ")}
                  </span>
                </div>
              </div>
            </div>

            <div className="flex items-center">
              <Calendar className="h-4 w-4 text-gray-400 mr-2" />
              <div>
                <p className="text-sm font-medium text-gray-900">Due Date</p>
                <p className="text-sm text-gray-500">
                  {currentAssignment.dueDate
                    ? new Date(currentAssignment.dueDate).toLocaleDateString()
                    : "No due date"}
                </p>
              </div>
            </div>

            <div className="flex items-center">
              <Clock className="h-4 w-4 text-gray-400 mr-2" />
              <div>
                <p className="text-sm font-medium text-gray-900">Created</p>
                <p className="text-sm text-gray-500">
                  {new Date(currentAssignment.createdAt).toLocaleDateString()}
                </p>
              </div>
            </div>
          </div>

          {/* Exercise Description */}
          {currentAssignment.exercise.description && (
            <div className="mt-6 pt-6 border-t border-gray-200">
              <h3 className="text-sm font-medium text-gray-900 mb-3">
                Exercise Description
              </h3>
              <RichTextDisplay
                content={currentAssignment.exercise.description}
                className="text-gray-700"
              />
            </div>
          )}
        </div>
      </Card>

      {/* Questions Form */}
      <form onSubmit={handleSubmit} className="space-y-6">
        {questions.map((question, index) => {
          if (question.type === "text_block") {
            return (
              <Card key={question.id}>
                <div className="p-6">
                  {(question as any).title && (
                    <h3 className="text-lg font-medium text-gray-900 mb-4">
                      {(question as any).title}
                    </h3>
                  )}
                  <RichTextDisplay
                    content={(question as any).content || ""}
                    className="prose max-w-none"
                  />
                </div>
              </Card>
            );
          }

          // Count only actual questions for numbering
          const questionNumber = questions
            .slice(0, index + 1)
            .filter((q) => q.type !== "text_block").length;

          return (
            <Card key={question.id}>
              <CardHeader
                title={`Question ${questionNumber}`}
                description={
                  (question as any).required ? "Required" : "Optional"
                }
              />
              <div className="p-6 pt-0">
                <div className="mb-4">
                  <h3 className="text-lg font-medium text-gray-900 mb-2">
                    {(question as any).prompt}
                  </h3>
                  {(question as any).description && (
                    <RichTextDisplay
                      content={(question as any).description}
                      className="text-sm text-gray-600 mb-4"
                    />
                  )}
                </div>
                {renderQuestion(question)}
              </div>
            </Card>
          );
        })}

        {/* Submit Section */}
        {!isCompleted && (
          <Card>
            <div className="p-6">
              {error && <ErrorMessage message={error} className="mb-4" />}

              {success && (
                <div className="flex items-center p-3 bg-green-50 border border-green-200 rounded-lg mb-4">
                  <CheckCircle className="h-5 w-5 text-green-600 mr-2" />
                  <span className="text-sm text-green-800">{success}</span>
                </div>
              )}

              <div className="flex space-x-3">
                <Button
                  type="submit"
                  disabled={loading.submitting}
                  className="flex-1"
                >
                  {loading.submitting ? "Submitting..." : "Submit Assignment"}
                </Button>
                <Button
                  type="button"
                  variant="secondary"
                  onClick={() => navigate("/dashboard/assignments")}
                >
                  Cancel
                </Button>
              </div>
            </div>
          </Card>
        )}

        {isCompleted && (
          <Card>
            <div className="p-6">
              <div className="flex items-center p-3 bg-green-50 border border-green-200 rounded-lg mb-4">
                <CheckCircle className="h-5 w-5 text-green-600 mr-2" />
                <span className="text-sm text-green-800">
                  This assignment has been completed and submitted.
                </span>
              </div>
              <Button
                type="button"
                onClick={() => navigate("/dashboard/assignments")}
              >
                Back to Assignments
              </Button>
            </div>
          </Card>
        )}
      </form>
    </div>
  );
};

export default AssignmentSubmissionForm;
